import requests
import zipfile
import os
from pathlib import Path
import platform

def download_wkhtmltopdf():
    """下载 wkhtmltopdf 到项目的 bin 目录"""
    
    # 检测系统架构
    system = platform.system().lower()
    machine = platform.machine().lower()
    
    if system == 'windows':
        if '64' in machine or 'amd64' in machine:
            url = "https://github.com/wkhtmltopdf/packaging/releases/download/0.12.6-1/wkhtmltox-0.12.6-1.msvc2015-win64.exe"
            filename = "wkhtmltox-win64.exe"
        else:
            url = "https://github.com/wkhtmltopdf/packaging/releases/download/0.12.6-1/wkhtmltox-0.12.6-1.msvc2015-win32.exe"
            filename = "wkhtmltox-win32.exe"
    else:
        print(f"暂不支持自动下载 {system} 系统的 wkhtmltopdf")
        print("请手动安装:")
        print("- Ubuntu/Debian: sudo apt-get install wkhtmltopdf")
        print("- macOS: brew install wkhtmltopdf")
        return False
    
    # 创建下载目录
    download_dir = Path(__file__).parent / "downloads"
    download_dir.mkdir(exist_ok=True)
    
    download_path = download_dir / filename
    
    print(f"正在下载 wkhtmltopdf...")
    print(f"URL: {url}")
    print(f"保存到: {download_path}")
    
    try:
        response = requests.get(url, stream=True)
        response.raise_for_status()
        
        total_size = int(response.headers.get('content-length', 0))
        downloaded = 0
        
        with open(download_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
                    downloaded += len(chunk)
                    if total_size > 0:
                        percent = (downloaded / total_size) * 100
                        print(f"\r下载进度: {percent:.1f}%", end='', flush=True)
        
        print(f"\n✓ 下载完成: {download_path}")
        print(f"📁 请手动运行安装程序: {download_path}")
        print("安装后，wkhtmltopdf 将可用于 PDF 转换")
        
        return True
        
    except Exception as e:
        print(f"✗ 下载失败: {e}")
        return False

def check_wkhtmltopdf():
    """检查 wkhtmltopdf 是否已安装"""
    try:
        import subprocess
        result = subprocess.run(['wkhtmltopdf', '--version'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✓ wkhtmltopdf 已安装")
            print(f"版本信息: {result.stdout.strip()}")
            return True
        else:
            print("✗ wkhtmltopdf 未正确安装")
            return False
    except FileNotFoundError:
        print("✗ wkhtmltopdf 未找到")
        return False

if __name__ == "__main__":
    print("检查 wkhtmltopdf 安装状态...")
    
    if not check_wkhtmltopdf():
        print("\n开始下载 wkhtmltopdf...")
        download_wkhtmltopdf()
    else:
        print("wkhtmltopdf 已可用，无需下载")
