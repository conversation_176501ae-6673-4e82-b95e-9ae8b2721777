import pypandoc
import os
from pathlib import Path
import subprocess
import webbrowser

def setup_custom_pandoc():
    """设置使用项目下的 pandoc.exe"""
    current_dir = Path(__file__).parent
    pandoc_path = current_dir / "bin" / "pandoc.exe"
    
    if not pandoc_path.exists():
        raise FileNotFoundError(f"未找到 pandoc.exe: {pandoc_path}")
    
    os.environ['PYPANDOC_PANDOC'] = str(pandoc_path)
    print(f"✓ 使用 pandoc 版本: {pypandoc.get_pandoc_version()}")

def method1_wkhtmltopdf(input_file, output_file):
    """方案1: 使用 wkhtmltopdf（需要单独安装）"""
    try:
        setup_custom_pandoc()
        output = pypandoc.convert_file(input_file, 'pdf', 
                                     outputfile=output_file,
                                     extra_args=['--pdf-engine=wkhtmltopdf'])
        print(f"✓ 使用 wkhtmltopdf 转换成功: {output_file}")
        return True
    except Exception as e:
        print(f"✗ wkhtmltopdf 转换失败: {e}")
        return False

def method2_html_intermediate(input_file, output_file):
    """方案2: 先转换为 HTML，然后提示用户手动转换"""
    try:
        setup_custom_pandoc()
        
        # 转换为自包含的 HTML
        html_file = output_file.replace('.pdf', '.html')
        output = pypandoc.convert_file(input_file, 'html', 
                                     outputfile=html_file,
                                     extra_args=[
                                         '--standalone', 
                                         '--self-contained',
                                         '--css=https://cdnjs.cloudflare.com/ajax/libs/github-markdown-css/5.2.0/github-markdown-light.min.css'
                                     ])
        
        print(f"✓ HTML 转换成功: {html_file}")
        print("📝 请在浏览器中打开 HTML 文件，然后使用 Ctrl+P 打印为 PDF")
        
        # 自动打开 HTML 文件
        webbrowser.open(html_file)
        return True
        
    except Exception as e:
        print(f"✗ HTML 转换失败: {e}")
        return False

def method3_direct_subprocess(input_file, output_file):
    """方案3: 直接调用 pandoc 命令行"""
    try:
        current_dir = Path(__file__).parent
        pandoc_path = current_dir / "bin" / "pandoc.exe"
        
        if not pandoc_path.exists():
            raise FileNotFoundError(f"未找到 pandoc.exe: {pandoc_path}")
        
        # 尝试不同的 PDF 引擎
        engines = ['wkhtmltopdf', 'weasyprint', 'prince']
        
        for engine in engines:
            try:
                cmd = [
                    str(pandoc_path),
                    input_file,
                    '-o', output_file,
                    f'--pdf-engine={engine}'
                ]
                
                print(f"尝试命令: {' '.join(cmd)}")
                result = subprocess.run(cmd, capture_output=True, text=True, check=True)
                print(f"✓ 使用 {engine} 转换成功: {output_file}")
                return True
                
            except subprocess.CalledProcessError as e:
                print(f"✗ {engine} 失败: {e.stderr}")
                continue
        
        return False
        
    except Exception as e:
        print(f"✗ 直接调用失败: {e}")
        return False

def method4_reportlab_fallback(input_file, output_file):
    """方案4: 使用 reportlab 作为备选方案"""
    try:
        import markdown
        from reportlab.lib.pagesizes import letter
        from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
        from reportlab.lib.styles import getSampleStyleSheet
        from reportlab.lib.units import inch
        from bs4 import BeautifulSoup
        
        # 读取 Markdown 文件
        with open(input_file, 'r', encoding='utf-8') as f:
            md_text = f.read()
        
        # 转换为 HTML
        html_text = markdown.markdown(md_text)
        soup = BeautifulSoup(html_text, 'html.parser')
        
        # 创建 PDF
        doc = SimpleDocTemplate(output_file, pagesize=letter)
        styles = getSampleStyleSheet()
        story = []
        
        # 简单的文本提取和格式化
        for element in soup.find_all(['h1', 'h2', 'h3', 'p', 'li']):
            text = element.get_text().strip()
            if text:
                if element.name == 'h1':
                    story.append(Paragraph(text, styles['Heading1']))
                elif element.name == 'h2':
                    story.append(Paragraph(text, styles['Heading2']))
                elif element.name == 'h3':
                    story.append(Paragraph(text, styles['Heading3']))
                else:
                    story.append(Paragraph(text, styles['Normal']))
                story.append(Spacer(1, 12))
        
        doc.build(story)
        print(f"✓ 使用 reportlab 转换成功: {output_file}")
        return True
        
    except ImportError:
        print("✗ reportlab 或相关依赖未安装")
        print("安装命令: pip install reportlab beautifulsoup4 markdown")
        return False
    except Exception as e:
        print(f"✗ reportlab 转换失败: {e}")
        return False

def convert_markdown_to_pdf(input_file='1.md', output_file='output.pdf'):
    """主转换函数，尝试多种方法"""
    
    print(f"开始转换: {input_file} -> {output_file}")
    print("=" * 50)
    
    # 按优先级尝试不同方法
    methods = [
        ("wkhtmltopdf 引擎", method1_wkhtmltopdf),
        ("HTML 中间格式", method2_html_intermediate),
        ("直接调用 pandoc", method3_direct_subprocess),
        ("reportlab 备选方案", method4_reportlab_fallback)
    ]
    
    for method_name, method_func in methods:
        print(f"\n🔄 尝试方法: {method_name}")
        if method_func(input_file, output_file):
            print(f"\n🎉 转换成功！使用方法: {method_name}")
            return True
        print(f"❌ {method_name} 失败，尝试下一个方法...")
    
    print("\n😞 所有转换方法都失败了")
    print("\n💡 建议解决方案:")
    print("1. 安装 wkhtmltopdf: https://wkhtmltopdf.org/downloads.html")
    print("2. 或使用在线转换工具")
    print("3. 或安装完整的 LaTeX 发行版")
    
    return False

if __name__ == "__main__":
    # 使用示例
    convert_markdown_to_pdf('1.md', 'output_solution.pdf')
