import pypandoc
import os
import webbrowser
from pathlib import Path

def setup_custom_pandoc():
    """设置使用项目下的 pandoc.exe"""
    current_dir = Path(__file__).parent
    pandoc_path = current_dir / "bin" / "pandoc.exe"
    
    if not pandoc_path.exists():
        raise FileNotFoundError(f"未找到 pandoc.exe: {pandoc_path}")
    
    os.environ['PYPANDOC_PANDOC'] = str(pandoc_path)
    print(f"✓ 使用 pandoc 版本: {pypandoc.get_pandoc_version()}")

def convert_md_to_html(input_file, output_file):
    """将 Markdown 转换为美观的 HTML"""
    try:
        setup_custom_pandoc()
        
        # 转换为自包含的 HTML，包含 CSS 样式
        output = pypandoc.convert_file(
            input_file, 
            'html', 
            outputfile=output_file,
            extra_args=[
                '--standalone',
                '--self-contained',
                '--css=https://cdnjs.cloudflare.com/ajax/libs/github-markdown-css/5.2.0/github-markdown-light.min.css',
                '--metadata', 'title=Markdown转换文档',
                '--template=<html><head><meta charset="utf-8"><title>$title$</title><style>body{box-sizing:border-box;min-width:200px;max-width:980px;margin:0 auto;padding:45px;}.markdown-body{font-family:-apple-system,BlinkMacSystemFont,"Segoe UI",Helvetica,Arial,sans-serif;font-size:16px;line-height:1.5;word-wrap:break-word;}</style>$for(css)$<link rel="stylesheet" href="$css$">$endfor$</head><body class="markdown-body">$body$</body></html>'
            ]
        )
        
        print(f"✓ HTML 转换成功: {output_file}")
        return True
        
    except Exception as e:
        print(f"✗ HTML 转换失败: {e}")
        return False

def convert_md_to_simple_html(input_file, output_file):
    """简单的 HTML 转换（备选方案）"""
    try:
        setup_custom_pandoc()
        
        output = pypandoc.convert_file(
            input_file, 
            'html', 
            outputfile=output_file,
            extra_args=['--standalone']
        )
        
        print(f"✓ 简单 HTML 转换成功: {output_file}")
        return True
        
    except Exception as e:
        print(f"✗ 简单 HTML 转换失败: {e}")
        return False

def add_print_styles(html_file):
    """为 HTML 文件添加打印样式"""
    try:
        with open(html_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 添加打印样式
        print_css = """
<style>
@media print {
    body { 
        margin: 0; 
        padding: 20px; 
        font-size: 12pt; 
        line-height: 1.4; 
    }
    .markdown-body {
        max-width: none;
        margin: 0;
        padding: 0;
    }
    h1, h2, h3, h4, h5, h6 {
        page-break-after: avoid;
        break-after: avoid;
    }
    p, li {
        page-break-inside: avoid;
        break-inside: avoid;
    }
    table {
        page-break-inside: avoid;
        break-inside: avoid;
    }
    @page {
        margin: 2cm;
        size: A4;
    }
}
</style>
"""
        
        # 在 </head> 前插入打印样式
        if '</head>' in content:
            content = content.replace('</head>', print_css + '</head>')
        else:
            # 如果没有 head 标签，在 body 前添加
            content = print_css + content
        
        with open(html_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✓ 已添加打印样式")
        return True
        
    except Exception as e:
        print(f"✗ 添加打印样式失败: {e}")
        return False

def main():
    """主函数"""
    input_file = "1.md"
    html_file = "output_styled.html"
    
    print("🔄 开始 Markdown 到 PDF 转换流程")
    print("=" * 50)
    
    # 检查输入文件
    if not Path(input_file).exists():
        print(f"✗ 输入文件不存在: {input_file}")
        return
    
    # 尝试转换为 HTML
    success = False
    
    print("📄 步骤 1: 转换为 HTML")
    if convert_md_to_html(input_file, html_file):
        success = True
    elif convert_md_to_simple_html(input_file, html_file):
        success = True
    
    if not success:
        print("✗ HTML 转换失败")
        return
    
    # 添加打印样式
    print("\n🎨 步骤 2: 优化打印样式")
    add_print_styles(html_file)
    
    # 打开 HTML 文件
    print(f"\n🌐 步骤 3: 打开 HTML 文件")
    try:
        webbrowser.open(html_file)
        print(f"✓ 已在浏览器中打开: {html_file}")
    except Exception as e:
        print(f"✗ 无法自动打开浏览器: {e}")
        print(f"请手动打开文件: {Path(html_file).absolute()}")
    
    # 提供转换为 PDF 的指导
    print(f"\n📋 步骤 4: 转换为 PDF")
    print("请按照以下步骤将 HTML 转换为 PDF:")
    print("1. 在浏览器中按 Ctrl+P (或 Cmd+P)")
    print("2. 选择 '另存为 PDF' 或 '打印到 PDF'")
    print("3. 调整页面设置:")
    print("   - 页面大小: A4")
    print("   - 边距: 默认或自定义")
    print("   - 背景图形: 开启（保留样式）")
    print("4. 点击 '保存' 或 '打印'")
    print(f"\n✅ HTML 文件已准备就绪: {Path(html_file).absolute()}")

if __name__ == "__main__":
    main()
