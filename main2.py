import pypandoc
import os
from pathlib import Path

# 设置自定义 pandoc 路径
# 获取当前脚本所在目录
current_dir = Path(__file__).parent
pandoc_path = current_dir / "bin" / "pandoc.exe"

# 设置环境变量指向项目下的 pandoc.exe
os.environ['PYPANDOC_PANDOC'] = str(pandoc_path)

# 验证 pandoc 路径是否正确
print(f"使用的 pandoc 路径: {pypandoc.get_pandoc_path()}")
print(f"pandoc 版本: {pypandoc.get_pandoc_version()}")

# 推荐的转换顺序：wkhtmltopdf -> HTML -> 其他方案
conversion_methods = [
    {
        'name': 'wkhtmltopdf',
        'args': ['--pdf-engine=wkhtmltopdf'],
        'description': '使用 wkhtmltopdf 引擎'
    },
    {
        'name': 'html_to_pdf',
        'args': ['--pdf-engine=wkhtmltopdf', '--standalone'],
        'description': '使用 wkhtmltopdf 引擎（独立模式）'
    },
    {
        'name': 'prince',
        'args': ['--pdf-engine=prince'],
        'description': '使用 Prince 引擎'
    }
]

success = False
for method in conversion_methods:
    try:
        print(f"\n尝试转换方法: {method['description']}")
        output = pypandoc.convert_file('1.md', 'pdf',
                                     outputfile='output.pdf',
                                     extra_args=method['args'])
        print(f"✓ PDF 转换成功！({method['description']})")
        success = True
        break
    except Exception as e:
        print(f"✗ {method['name']} 转换失败: {str(e)[:100]}...")

if not success:
    print("\n所有 PDF 转换方法都失败了。")
    print("建议解决方案：")
    print("1. 下载并安装 wkhtmltopdf: https://wkhtmltopdf.org/downloads.html")
    print("2. 或者先转换为 HTML，然后手动转换为 PDF")

    # 尝试转换为 HTML 作为备选方案
    try:
        print("\n尝试转换为 HTML...")
        html_output = pypandoc.convert_file('1.md', 'html',
                                          outputfile='output.html',
                                          extra_args=['--standalone', '--self-contained'])
        print("✓ HTML 转换成功！你可以在浏览器中打开 output.html 并打印为 PDF")
    except Exception as e:
        print(f"✗ HTML 转换也失败了: {e}")
