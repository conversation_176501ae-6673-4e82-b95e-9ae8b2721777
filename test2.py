import markdown
import pdfkit

# 1. 读取 Markdown 文件
with open('1.md', 'r', encoding='utf-8') as f:
    md_text = f.read()

# 2. 将 Markdown 转换为 HTML
html = markdown.markdown(md_text)

# 3. 添加 CSS 样式（可选）
html = f"""
<html>
<head>
    <meta charset="utf-8">
    <style>
        body {{ font-family: Arial; line-height: 1.6; }}
        h1, h2, h3 {{ color: #333; }}
        code {{ background: #f4f4f4; padding: 2px 5px; }}
        pre {{ background: #f4f4f4; padding: 10px; overflow: auto; }}
    </style>
</head>
<body>
{html}
</body>
</html>
"""

config = pdfkit.configuration(wkhtmltopdf='bin/wkhtmltox/bin/wkhtmltopdf.exe')
pdfkit.from_string(html, 'out.pdf', configuration=config)

# 4. 将 HTML 转换为 PDF
# pdfkit.from_string(html, 'output.pdf')

print("PDF 生成成功！")