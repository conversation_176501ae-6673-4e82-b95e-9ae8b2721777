import os
import subprocess
from pathlib import Path

def convert_md_to_html_direct(input_file, output_file):
    """直接使用 pandoc.exe 转换为 HTML"""
    try:
        # 检查文件是否存在
        if not Path(input_file).exists():
            print(f"✗ 输入文件不存在: {input_file}")
            return False
        
        if not Path("bin/pandoc.exe").exists():
            print("✗ 未找到 bin/pandoc.exe")
            return False
        
        # 构建命令
        cmd = [
            "bin/pandoc.exe",
            input_file,
            "-o", output_file,
            "--standalone",
            "--self-contained",
            "--metadata", "title=Markdown转换文档"
        ]
        
        print(f"执行命令: {' '.join(cmd)}")
        
        # 执行转换
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print(f"✓ HTML 转换成功: {output_file}")
        
        # 添加打印样式
        add_print_styles_to_html(output_file)
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"✗ 转换失败: {e}")
        if e.stderr:
            print(f"错误信息: {e.stderr}")
        return False
    except Exception as e:
        print(f"✗ 转换失败: {e}")
        return False

def add_print_styles_to_html(html_file):
    """为 HTML 文件添加打印样式和美化样式"""
    try:
        with open(html_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 添加完整的样式
        styles = """
<style>
/* 基础样式 */
body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    background-color: #fff;
}

/* 标题样式 */
h1, h2, h3, h4, h5, h6 {
    margin-top: 24px;
    margin-bottom: 16px;
    font-weight: 600;
    line-height: 1.25;
}

h1 { font-size: 2em; border-bottom: 1px solid #eaecef; padding-bottom: 10px; }
h2 { font-size: 1.5em; border-bottom: 1px solid #eaecef; padding-bottom: 8px; }
h3 { font-size: 1.25em; }
h4 { font-size: 1em; }

/* 段落和列表 */
p { margin-bottom: 16px; }
ul, ol { margin-bottom: 16px; padding-left: 30px; }
li { margin-bottom: 4px; }

/* 表格样式 */
table {
    border-collapse: collapse;
    width: 100%;
    margin-bottom: 16px;
}
th, td {
    border: 1px solid #dfe2e5;
    padding: 6px 13px;
    text-align: left;
}
th {
    background-color: #f6f8fa;
    font-weight: 600;
}

/* 代码样式 */
code {
    background-color: #f6f8fa;
    border-radius: 3px;
    font-size: 85%;
    margin: 0;
    padding: 0.2em 0.4em;
}

pre {
    background-color: #f6f8fa;
    border-radius: 6px;
    font-size: 85%;
    line-height: 1.45;
    overflow: auto;
    padding: 16px;
}

/* 引用样式 */
blockquote {
    border-left: 4px solid #dfe2e5;
    margin: 0;
    padding: 0 16px;
    color: #6a737d;
}

/* 打印样式 */
@media print {
    body {
        margin: 0;
        padding: 15mm;
        font-size: 12pt;
        line-height: 1.4;
        max-width: none;
        background-color: white;
    }
    
    h1, h2, h3, h4, h5, h6 {
        page-break-after: avoid;
        break-after: avoid;
    }
    
    p, li {
        page-break-inside: avoid;
        break-inside: avoid;
    }
    
    table {
        page-break-inside: avoid;
        break-inside: avoid;
    }
    
    img {
        max-width: 100%;
        page-break-inside: avoid;
    }
    
    @page {
        margin: 20mm;
        size: A4;
    }
    
    /* 隐藏不需要打印的元素 */
    .no-print {
        display: none;
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    body {
        padding: 10px;
    }
}
</style>
"""
        
        # 在 </head> 前插入样式，如果没有 head 标签则创建
        if '<head>' in content and '</head>' in content:
            content = content.replace('</head>', styles + '</head>')
        elif '<html>' in content:
            # 如果有 html 标签但没有 head，在 html 后添加 head
            content = content.replace('<html>', '<html>\n<head>' + styles + '</head>')
        else:
            # 如果都没有，在开头添加
            content = '<html>\n<head>' + styles + '</head>\n<body>\n' + content + '\n</body>\n</html>'
        
        # 添加打印按钮（可选）
        print_button = '''
<div class="no-print" style="position: fixed; top: 10px; right: 10px; z-index: 1000;">
    <button onclick="window.print()" style="
        background-color: #0366d6;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 6px;
        cursor: pointer;
        font-size: 14px;
    ">打印为 PDF</button>
</div>
'''
        
        # 在 body 开始后添加打印按钮
        if '<body>' in content:
            content = content.replace('<body>', '<body>' + print_button)
        
        with open(html_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✓ 已添加样式和打印功能")
        return True
        
    except Exception as e:
        print(f"✗ 添加样式失败: {e}")
        return False

def open_html_file(html_file):
    """打开 HTML 文件"""
    try:
        import webbrowser
        webbrowser.open(html_file)
        print(f"✓ 已在浏览器中打开: {html_file}")
        return True
    except Exception as e:
        print(f"✗ 无法自动打开浏览器: {e}")
        print(f"请手动打开文件: {Path(html_file).absolute()}")
        return False

def main():
    """主函数"""
    input_file = "1.md"
    output_file = "output_simple.html"
    
    print("🔄 开始 Markdown 到 HTML 转换")
    print("=" * 50)
    
    # 转换为 HTML
    if convert_md_to_html_direct(input_file, output_file):
        print(f"\n✅ 转换成功！")
        
        # 打开 HTML 文件
        print(f"\n🌐 打开 HTML 文件...")
        open_html_file(output_file)
        
        # 提供使用说明
        print(f"\n📋 如何转换为 PDF:")
        print("1. 在浏览器中点击右上角的 '打印为 PDF' 按钮")
        print("2. 或者按 Ctrl+P (Windows) 或 Cmd+P (Mac)")
        print("3. 在打印对话框中选择 '另存为 PDF'")
        print("4. 调整设置:")
        print("   - 页面: 全部")
        print("   - 布局: 纵向")
        print("   - 页面大小: A4")
        print("   - 边距: 默认")
        print("   - 选项: 勾选 '背景图形'")
        print("5. 点击 '保存' 完成转换")
        
        print(f"\n📁 HTML 文件位置: {Path(output_file).absolute()}")
        
    else:
        print("\n❌ 转换失败")
        print("请检查:")
        print("1. bin/pandoc.exe 是否存在")
        print("2. 1.md 文件是否存在")
        print("3. 是否有写入权限")

if __name__ == "__main__":
    main()
