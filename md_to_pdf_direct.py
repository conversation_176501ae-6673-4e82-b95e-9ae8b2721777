import subprocess
import os
from pathlib import Path

def markdown_to_pdf_direct(input_md, output_pdf, pdf_engine=None):
    """
    直接使用项目下的 pandoc.exe 将 Markdown 转换为 PDF
    
    Args:
        input_md: 输入的 Markdown 文件
        output_pdf: 输出的 PDF 文件
        pdf_engine: PDF 引擎 ('wkhtmltopdf', 'weasyprint', 'prince' 等)
    """
    try:
        # 构建命令
        cmd = ["bin/pandoc.exe", input_md, "-o", output_pdf]
        
        # 添加 PDF 引擎参数
        if pdf_engine:
            cmd.extend([f"--pdf-engine={pdf_engine}"])
        
        print(f"执行命令: {' '.join(cmd)}")
        
        # 执行转换
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print(f"✓ 成功将 {input_md} 转换为 {output_pdf}")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"✗ 转换失败: {e}")
        if e.stderr:
            print(f"错误信息: {e.stderr}")
        return False
    except FileNotFoundError:
        print("✗ 未找到 bin/pandoc.exe")
        return False

def try_multiple_engines(input_md, output_pdf):
    """尝试多种 PDF 引擎"""
    
    # 可用的 PDF 引擎列表
    engines = [
        None,  # 默认引擎 (通常是 pdflatex)
        'wkhtmltopdf',
        'weasyprint', 
        'prince',
        'pagedjs-cli',
        'context'
    ]
    
    print(f"开始转换: {input_md} -> {output_pdf}")
    print("=" * 50)
    
    for engine in engines:
        engine_name = engine if engine else "默认引擎"
        print(f"\n🔄 尝试使用: {engine_name}")
        
        if markdown_to_pdf_direct(input_md, output_pdf, engine):
            print(f"🎉 转换成功！使用引擎: {engine_name}")
            return True
        
        print(f"❌ {engine_name} 失败")
    
    print("\n😞 所有 PDF 引擎都失败了")
    return False

def convert_to_html_fallback(input_md, output_html):
    """备选方案：转换为 HTML"""
    try:
        cmd = [
            "bin/pandoc.exe", 
            input_md, 
            "-o", output_html,
            "--standalone",
            "--self-contained"
        ]
        
        print(f"执行命令: {' '.join(cmd)}")
        subprocess.run(cmd, check=True, capture_output=True, text=True)
        print(f"✓ 成功转换为 HTML: {output_html}")
        print("💡 你可以在浏览器中打开 HTML 文件，然后打印为 PDF")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"✗ HTML 转换也失败了: {e}")
        return False

def main():
    """主函数"""
    input_file = "1.md"
    output_pdf = "output_direct.pdf"
    output_html = "output_direct.html"
    
    # 检查输入文件是否存在
    if not Path(input_file).exists():
        print(f"✗ 输入文件不存在: {input_file}")
        return
    
    # 检查 pandoc.exe 是否存在
    if not Path("bin/pandoc.exe").exists():
        print("✗ 未找到 bin/pandoc.exe")
        print("请确保 pandoc.exe 在 bin 目录下")
        return
    
    # 尝试转换为 PDF
    if try_multiple_engines(input_file, output_pdf):
        print(f"\n🎉 PDF 转换完成: {output_pdf}")
    else:
        print(f"\n🔄 PDF 转换失败，尝试转换为 HTML...")
        if convert_to_html_fallback(input_file, output_html):
            print(f"\n💡 建议：在浏览器中打开 {output_html}，然后使用 Ctrl+P 打印为 PDF")
        else:
            print("\n😞 所有转换方法都失败了")
            print("\n💡 建议解决方案:")
            print("1. 安装 wkhtmltopdf: https://wkhtmltopdf.org/downloads.html")
            print("2. 安装 LaTeX 发行版 (如 MiKTeX 或 TeX Live)")
            print("3. 使用在线 Markdown 转 PDF 工具")

if __name__ == "__main__":
    main()
